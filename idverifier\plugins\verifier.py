import re
import asyncio
from datetime import datetime, timedelta, timezone
from pyrogram import Client, filters
from pyrogram.types import (
    Message,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
)
from idverifier import <PERSON><PERSON><PERSON>, LOGS_CHANNEL_ID, LOGGER, VIP_GROUP_ID
from idverifier.utils.constants import NOT_REG, CLOSE_ACC, BALANCE_ZERO, BALANCE_LESS_45, BALANCE_MORE_45, WRONG_ID, TRADER_ID_ALREADY_USED, CURRENCY_THRESHOLDS
from idverifier.db import add_verified_user, get_user_by_trader_id, get_all_verified_users, save_turnover_record, get_last_turnover_check, get_turnover_history, remove_user
from __main__ import user, app
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore




jobstores = {"default": SQLAlchemyJobStore(url="sqlite:///jobs.sqlite")}
scheduler = AsyncIOScheduler(jobstores=jobstores)
scheduler.start()
LOGGER.info("Scheduler initialized and started")


@Client.on_message(filters.photo | filters.video | filters.document | filters.audio & filters.private)
async def handle_media(client: Client, message: Message):
    await message.reply_text(WRONG_ID.format(message.from_user.mention))
    

@Client.on_message(~filters.regex(r"^/") & filters.text & filters.private)
async def handle_user_id(client: Client, message: Message):
    try:
        user_input = message.text.strip()
        mention = message.from_user.mention
        balance = 0
        
        if not user_input.isdigit() or len(user_input) > 9:
            await message.reply_text(WRONG_ID.format(mention))
            return
        
        if user_input.isdigit():
            # Check if trader ID is already used by another user
            existing_user = await get_user_by_trader_id(user_input)
            if existing_user and existing_user['user_id'] != message.from_user.id:
                await message.reply_text(TRADER_ID_ALREADY_USED.format(mention))
                return
            
            processing_msg = await message.reply_text("🔄 Checking your status...")
            
            response = await forward_to_quotex_bot(user_input)
            await processing_msg.delete()
            
            if response == "Not Found":
                await message.reply_text(NOT_REG.format(mention))
                return
            
            closematch = re.search(r'\bACCOUNT CLOSED\b', response)            
            if closematch:
                await message.reply_text(CLOSE_ACC.format(mention))
                return
            else:
                if not re.search(r'\bACCOUNT CLOSED\b', response):
                    print(f"Full response: {response}")
                    balance_match = re.search(r'Balance:\s*([₹$₨৳])\s*([\d,.]+)', response)
                    if balance_match:
                        currency_symbol = balance_match.group(1)
                        balance = balance_match.group(2)
                        balance = balance.replace(',', '')
                        balance_float = float(balance)
                        
                        # Get minimum threshold for this currency
                        min_threshold = CURRENCY_THRESHOLDS.get(currency_symbol, 45)
                        
                        if balance_float == 0:
                            await message.reply_text(BALANCE_ZERO.format(mention))
                            return
                        elif balance_float < min_threshold and balance_float > 0:
                            await message.reply_text(BALANCE_LESS_45.format(mention, currency_symbol, min_threshold))
                            return
                        elif balance_float >= min_threshold:
                            try:
                                user_stored = await add_verified_user(
                                    user_id=message.from_user.id,
                                    trader_id=user_input,
                                    balance=balance_float,
                                    username=message.from_user.username,
                                    first_name=message.from_user.first_name
                                )
                                
                                if user_stored:
                                    LOGGER.info(f"Stored user {message.from_user.id} with trader ID {user_input} and balance {currency_symbol}{balance}")
                                    await app.send_message(LOGS_CHANNEL_ID, f"User {mention} ({message.from_user.id}) with trader ID {user_input} and balance {currency_symbol}{balance} has been verified")
                                
                                invite_link = await app.create_chat_invite_link(
                                    VIP_GROUP_ID,
                                    creates_join_request=False,
                                    member_limit=1,
                                    name=f"Invite for {message.from_user.first_name} (ID: {user_input})"
                                )
                                await message.reply_text(BALANCE_MORE_45.format(mention, invite_link.invite_link))
                            except Exception as e:
                                LOGGER.error(f"Error processing user verification: {e}")
                                await message.reply_text(f"✅ You're eligible for VIP access! Please contact admin for invite link.\nYour balance: {currency_symbol}{balance}")
                            return
                            
        else:
            await message.reply_text(
                "💡 **How to use this bot:**\n\n"
                "Simply send a numeric user ID (like `236789`) and I'll verify it for you.\n\n"
                "Example: Just type `236789` and send it."
            )
            
    except Exception as e:
        LOGGER.error(f"Error in handle_user_id: {e}")
        await message.reply_text("❌ **An error occurred.** Please try again later.")


async def forward_to_quotex_bot(input_id: str):
    try:
        quotex_bot_username = "QuotexPartnerBot"
        LOGGER.info(f"Forwarding user ID {input_id} to {quotex_bot_username}")
        await user.send_message(quotex_bot_username, input_id)
        response = await wait_for_response(quotex_bot_username, input_id, timeout=30)
        
        return response
        
    except Exception as e:
        LOGGER.error(f"Error forwarding to QuotexPartnerBot: {e}")
        return None


async def wait_for_response(bot_username: str, input_id: str, timeout: int = 30):
    try:
        start_time = datetime.now(timezone.utc)
        
        while (datetime.now(timezone.utc) - start_time).seconds < timeout:
            try:
                async for message in user.get_chat_history(bot_username, limit=5):                    
                    match = re.search(r'Trader\s+#\s*(\d+)', message.text)
                    if match:
                        trader_id = match.group(1)
                        if trader_id == input_id:
                            return message.text
                    match2 = re.search(r"Trader with ID\s*=\s*'(\d+)'", message.text)
                    if match2:
                        trader_id = match2.group(1)
                        if trader_id == input_id:
                            return "Not Found"
                await asyncio.sleep(2)
            except Exception as e:
                LOGGER.error(f"Error checking messages: {e}")
                await asyncio.sleep(2)
        
        LOGGER.warning(f"Timeout waiting for response from {bot_username}")
        return None
        
    except Exception as e:
        LOGGER.error(f"Error waiting for response: {e}")
        return None


@Client.on_message(filters.command("start") & filters.private)
async def start_command(client: Client, message: Message):
    """Handle /start command"""
    await message.reply_text(
        "🤖 **Welcome to ID Verifier Bot!**\n\n"
        "💡 **How to use:**\n"
        "Simply send me a numeric user ID and I'll verify it for you through QuotexPartnerBot.\n\n"
        "📝 **Example:**\n"
        "Send: `236789`\n"
        "I'll forward it to QuotexPartnerBot and return the result.\n\n"
        "🚀 **Ready to verify IDs!**"
    )


@Client.on_message(filters.command("help") & filters.private)
async def help_command(client: Client, message: Message):
    """Handle /help command"""
    user_id = message.from_user.id
    
    if user_id in ADMINS:
        # Admin help with all commands
        await message.reply_text(
            "❓ **ID Verifier Bot Help (Admin):**\n\n"
            "**User Commands:**\n"
            "• Send numeric ID to verify (e.g., `236789`)\n"
            "• `/start` - Welcome message\n"
            "• `/help` - This help message\n\n"
            "**Admin Commands:**\n"
            "• `/check_turnover` - Manual turnover check\n"
            "• `/turnover_history <user_id>` - View user's turnover history\n"
            "• `/scheduler_status` - Check APScheduler status\n"
            "• `/setup_scheduler` - Setup all schedulers\n"
            "• `/run_check_now` - Run turnover check immediately\n"
            "• `/run_balance_check` - Run daily balance check immediately\n"
            "• `/remove <trader_id>` - Remove a trader ID from database\n"
            "• `/check_trader <trader_id>` - Check trader ID information\n\n"
            "**Automation:**\n"
            "🔄 Turnover checks run automatically every 15 days\n"
            "📊 Daily balance checks run every day at 12:00 UTC\n"
            "⚡ Users below thresholds are automatically removed\n"
            "💾 All data is saved with SQLite persistence"
        )
    else:
        # Regular user help
        await message.reply_text(
            "❓ **How to use ID Verifier Bot:**\n\n"
            "1️⃣ Send me any numeric user ID (like `236789`)\n"
            "2️⃣ I'll forward it to QuotexPartnerBot\n"
            "3️⃣ You'll receive the verification result\n\n"
            "📋 **Available Commands:**\n"
            "• `/start` - Welcome message\n"
            "• `/help` - This help message\n\n"
            "💡 **Just send a number to get started!**"
        )


def extract_turnover_all(text: str) -> float | None:
    match = re.search(r"Turnover All[:\s]*([\d,]+\.\d+)", text)
    if match:
        value_str = match.group(1).replace(",", "")
        return float(value_str)
    return None


async def check_turnover(client: Client, message: Message):
    try:
        # Use app instance if client is None (automation call)
        bot_client = client if client else app
        
        users = await get_all_verified_users()
        
        # Define turnover thresholds based on days since verification
        turnover_thresholds = {
            15: 100, 30: 200, 45: 300, 60: 400, 75: 500, 90: 600,
            105: 700, 120: 800, 135: 900, 150: 1000, 165: 1100,
            180: 1200, 195: 1300, 210: 1400, 225: 1500, 240: 1600,
            255: 1700, 270: 1800, 285: 1900, 300: 2000, 315: 2100,
            330: 2200, 345: 2300, 360: 2400
        }
        
        for user in users:
            user_id = user['user_id']
            trader_id = user['trader_id']
            username = user.get('username', 'Unknown')
            verified_at = user['verified_at']
            
            # Calculate days since verification
            # Ensure verified_at is timezone-aware (assume UTC if naive)
            if verified_at.tzinfo is None:
                verified_at = verified_at.replace(tzinfo=timezone.utc)
            days_since_verification = (datetime.now(timezone.utc) - verified_at).days
            
            # Find the applicable threshold
            applicable_threshold = None
            for day_threshold in sorted(turnover_thresholds.keys()):
                if days_since_verification >= day_threshold:
                    applicable_threshold = turnover_thresholds[day_threshold]
                else:
                    break
            
            if applicable_threshold is None:
                LOGGER.info(f"User {user_id}:{trader_id} is still within the initial 15-day period")
                continue
            
            response = await forward_to_quotex_bot(trader_id)
            
            if response == "Not Found":
                await bot_client.ban_chat_member(VIP_GROUP_ID, user_id, datetime.now(timezone.utc) + timedelta(seconds=31))
                await remove_user(user_id)
                LOGGER.info(f"Kicked user {user_id}:{trader_id} from VIP group - trader not found (removed from DB)")
                continue
            
            closematch = re.search(r'\bACCOUNT CLOSED\b', response)            
            if closematch:
                await bot_client.ban_chat_member(VIP_GROUP_ID, user_id, datetime.now(timezone.utc) + timedelta(seconds=31))
                await remove_user(user_id)
                LOGGER.info(f"Kicked user {user_id}:{trader_id} from VIP group - account closed (removed from DB)")
                continue
            
            turnover_all = extract_turnover_all(response)
            if turnover_all is not None:
                # Save turnover to database
                await save_turnover_record(user_id, trader_id, turnover_all, days_since_verification)
                
                if turnover_all < applicable_threshold:
                    await bot_client.send_message(LOGS_CHANNEL_ID, f"User [{user_id}](tg://user?id={user_id}): Trader ID {trader_id} - turnover ${turnover_all} below threshold ${applicable_threshold} for day {days_since_verification} (removed from DB)")
                    await bot_client.send_message(user_id, f"Your turnover is below the threshold for day {days_since_verification}. Please increase your turnover to be eligible for VIP access.")
                    await bot_client.ban_chat_member(VIP_GROUP_ID, user_id, datetime.now(timezone.utc) + timedelta(seconds=31))
                    await remove_user(user_id)
                    LOGGER.info(f"Kicked user {user_id}:{trader_id} - turnover ${turnover_all} below threshold ${applicable_threshold} for day {days_since_verification} (removed from DB)")
                else:
                    LOGGER.info(f"User {user_id}:{trader_id} passed check - turnover ${turnover_all} above threshold ${applicable_threshold}")
            else:
                LOGGER.warning(f"Could not extract turnover for user {user_id}:{trader_id}")
                             
    except Exception as e:
        LOGGER.error(f"Error in check_turnover: {e}")


@Client.on_message(filters.command("check_turnover") & filters.user(ADMINS))
async def manual_turnover_check(client: Client, message: Message):
    """Manual command for admins to trigger turnover check"""
    try:
        await message.reply_text("🔄 Starting turnover check for all users...")
        await check_turnover(client, message)
        await message.reply_text("✅ Turnover check completed!")
    except Exception as e:
        LOGGER.error(f"Error in manual turnover check: {e}")
        await message.reply_text(f"❌ Error during turnover check: {str(e)}")


@Client.on_message(filters.command("turnover_history") & filters.user(ADMINS))
async def view_turnover_history(client: Client, message: Message):
    """Admin command to view turnover history for a user"""
    try:
        command_parts = message.text.split()
        if len(command_parts) != 2:
            await message.reply_text("📋 **Usage:** `/turnover_history <user_id>`\n\nExample: `/turnover_history 123456789`")
            return
        
        user_id = int(command_parts[1])
        history = await get_turnover_history(user_id)
        
        if not history:
            await message.reply_text(f"❌ No turnover history found for user {user_id}")
            return
        
        history_text = f"📈 **Turnover History for User {user_id}:**\n\n"
        for record in history[:10]:  # Show last 10 records
            recorded_date = record['recorded_at'].strftime('%Y-%m-%d %H:%M')
            history_text += f"• **Day {record['days_since_verification']}:** ${record['turnover']:.2f} ({recorded_date})\n"
        
        if len(history) > 10:
            history_text += f"\n... and {len(history) - 10} more records"
        
        await message.reply_text(history_text)
        
    except ValueError:
        await message.reply_text("❌ Please provide a valid user ID (numeric)")
    except Exception as e:
        LOGGER.error(f"Error viewing turnover history: {e}")
        await message.reply_text(f"❌ Error: {str(e)}")


@Client.on_message(filters.command("remove") & filters.user(ADMINS))
async def remove_trader_id(client: Client, message: Message):
    """Admin command to remove a trader ID from database"""
    try:
        command_parts = message.text.split()
        if len(command_parts) != 2:
            await message.reply_text("📋 **Usage:** `/remove <trader_id>`\n\nExample: `/remove 123456789`")
            return
        
        trader_id = command_parts[1].strip()
        
        # Validate trader ID format
        if not trader_id.isdigit():
            await message.reply_text("❌ Please provide a valid numeric trader ID")
            return
        
        # Check if trader ID exists in database
        user_data = await get_user_by_trader_id(trader_id)
        
        if not user_data:
            await message.reply_text(f"❌ **Trader ID `{trader_id}` not found in database**")
            return
        
        # Get user information for confirmation
        user_id = user_data['user_id']
        username = user_data.get('username', 'N/A')
        first_name = user_data.get('first_name', 'N/A')
        balance = user_data.get('balance', 0)
        verified_at = user_data.get('verified_at')
        
        # Format verification date
        verification_date = "Unknown"
        if verified_at:
            if verified_at.tzinfo is None:
                verified_at = verified_at.replace(tzinfo=timezone.utc)
            verification_date = verified_at.strftime('%Y-%m-%d %H:%M UTC')
        
        # Remove the user
        removal_success = await remove_user(user_id)
        
        if removal_success:
            # Create detailed removal report
            removal_report = (
                f"✅ **Trader ID Successfully Removed**\n\n"
                f"**Trader ID:** `{trader_id}`\n"
                f"**User ID:** `{user_id}`\n"
                f"**Name:** {first_name}\n"
                f"**Username:** @{username}\n"
                f"**Balance:** ${balance}\n"
                f"**Verified:** {verification_date}\n\n"
                f"🗑️ **User completely removed from database**"
            )
            
            await message.reply_text(removal_report)
            LOGGER.info(f"Admin {message.from_user.id} manually removed trader ID {trader_id} (user: {user_id})")
        else:
            await message.reply_text(f"❌ **Failed to remove trader ID `{trader_id}`**\nPlease check logs for details.")
        
    except ValueError:
        await message.reply_text("❌ Please provide a valid trader ID (numeric)")
    except Exception as e:
        LOGGER.error(f"Error in remove trader ID command: {e}")
        await message.reply_text(f"❌ Error: {str(e)}")


@Client.on_message(filters.command("check_trader") & filters.user(ADMINS))
async def check_trader_info(client: Client, message: Message):
    """Admin command to check trader ID information"""
    try:
        command_parts = message.text.split()
        if len(command_parts) != 2:
            await message.reply_text("📋 **Usage:** `/check_trader <trader_id>`\n\nExample: `/check_trader 123456789`")
            return
        
        trader_id = command_parts[1].strip()
        
        # Validate trader ID format
        if not trader_id.isdigit():
            await message.reply_text("❌ Please provide a valid numeric trader ID")
            return
        
        # Check if trader ID exists in database
        user_data = await get_user_by_trader_id(trader_id)
        
        if not user_data:
            await message.reply_text(f"❌ **Trader ID `{trader_id}` not found in database**")
            return
        
        # Get user information
        user_id = user_data['user_id']
        username = user_data.get('username', 'N/A')
        first_name = user_data.get('first_name', 'N/A')
        balance = user_data.get('balance', 0)
        verified_at = user_data.get('verified_at')
        is_active = user_data.get('is_active', False)
        
        # Format verification date
        verification_date = "Unknown"
        if verified_at:
            if verified_at.tzinfo is None:
                verified_at = verified_at.replace(tzinfo=timezone.utc)
            verification_date = verified_at.strftime('%Y-%m-%d %H:%M UTC')
        
        # Calculate days since verification
        days_since_verification = "Unknown"
        if verified_at:
            days_since_verification = (datetime.now(timezone.utc) - verified_at).days
        
        # Create information report
        status_emoji = "✅" if is_active else "❌"
        info_report = (
            f"📊 **Trader ID Information**\n\n"
            f"**Trader ID:** `{trader_id}`\n"
            f"**User ID:** `{user_id}`\n"
            f"**Name:** {first_name}\n"
            f"**Username:** @{username}\n"
            f"**Balance:** ${balance}\n"
            f"**Status:** {status_emoji} {'Active' if is_active else 'Inactive'}\n"
            f"**Verified:** {verification_date}\n"
            f"**Days Since Verification:** {days_since_verification}\n\n"
            f"💡 **Use `/remove {trader_id}` to remove this trader**"
        )
        
        await message.reply_text(info_report)
        
    except ValueError:
        await message.reply_text("❌ Please provide a valid trader ID (numeric)")
    except Exception as e:
        LOGGER.error(f"Error in check trader info command: {e}")
        await message.reply_text(f"❌ Error: {str(e)}")


async def daily_balance_check():
    """Check all verified users' balances and kick those below threshold"""
    try:
        LOGGER.info("🔄 Starting daily balance check...")
        
        users = await get_all_verified_users()
        kicked_count = 0
        
        for user in users:
            user_id = user['user_id']
            trader_id = user['trader_id']
            username = user.get('username', 'Unknown')
            first_name = user.get('first_name', 'Unknown')
            current_balance = user.get('balance', 0)
            
            try:
                # Check current balance via QuotexPartnerBot
                response = await forward_to_quotex_bot(trader_id)
                
                if response == "Not Found":
                    # Account not found - kick user
                    await app.ban_chat_member(VIP_GROUP_ID, user_id, datetime.now(timezone.utc) + timedelta(seconds=31))
                    await remove_user(user_id)
                    await app.send_message(
                        LOGS_CHANNEL_ID, 
                        f"🚫 **Daily Balance Check - Account Not Found**\n\n"
                        f"**User:** [{first_name}](tg://user?id={user_id}) (@{username})\n"
                        f"**User ID:** `{user_id}`\n"
                        f"**Trader ID:** `{trader_id}`\n"
                        f"**Previous Balance:** ${current_balance}\n"
                        f"**Reason:** Account not found/deleted\n"
                        f"**Action:** Removed from VIP group and database"
                    )
                    kicked_count += 1
                    LOGGER.info(f"Kicked user {user_id}:{trader_id} - account not found")
                    continue
                
                # Check if account is closed
                if re.search(r'\bACCOUNT CLOSED\b', response):
                    await app.ban_chat_member(VIP_GROUP_ID, user_id, datetime.now(timezone.utc) + timedelta(seconds=31))
                    await remove_user(user_id)
                    await app.send_message(
                        LOGS_CHANNEL_ID, 
                        f"🚫 **Daily Balance Check - Account Closed**\n\n"
                        f"**User:** [{first_name}](tg://user?id={user_id}) (@{username})\n"
                        f"**User ID:** `{user_id}`\n"
                        f"**Trader ID:** `{trader_id}`\n"
                        f"**Previous Balance:** ${current_balance}\n"
                        f"**Reason:** Account closed\n"
                        f"**Action:** Removed from VIP group and database"
                    )
                    kicked_count += 1
                    LOGGER.info(f"Kicked user {user_id}:{trader_id} - account closed")
                    continue
                
                # Extract current balance and currency
                balance_match = re.search(r'Balance:\s*([₹$₨৳])\s*([\d,.]+)', response)
                if balance_match:
                    currency_symbol = balance_match.group(1)
                    balance_str = balance_match.group(2).replace(',', '')
                    current_balance_float = float(balance_str)
                    
                    # Get threshold for this currency
                    min_threshold = CURRENCY_THRESHOLDS.get(currency_symbol, 45)
                    
                    # Check if balance is below threshold
                    if current_balance_float < min_threshold:
                        await app.ban_chat_member(VIP_GROUP_ID, user_id, datetime.now(timezone.utc) + timedelta(seconds=31))
                        await remove_user(user_id)
                        await app.send_message(
                            LOGS_CHANNEL_ID, 
                            f"🚫 **Daily Balance Check - Insufficient Balance**\n\n"
                            f"**User:** [{first_name}](tg://user?id={user_id}) (@{username})\n"
                            f"**User ID:** `{user_id}`\n"
                            f"**Trader ID:** `{trader_id}`\n"
                            f"**Current Balance:** {currency_symbol}{balance_str}\n"
                            f"**Required Minimum:** {currency_symbol}{min_threshold}\n"
                            f"**Reason:** Balance below minimum threshold\n"
                            f"**Action:** Removed from VIP group and database"
                        )
                        kicked_count += 1
                        LOGGER.info(f"Kicked user {user_id}:{trader_id} - balance {currency_symbol}{balance_str} below threshold {currency_symbol}{min_threshold}")
                        
                        # Notify the user
                        try:
                            await app.send_message(
                                user_id,
                                f"❌ **VIP Access Revoked**\n\n"
                                f"Your balance ({currency_symbol}{balance_str}) is below the required minimum ({currency_symbol}{min_threshold}).\n\n"
                                f"To regain VIP access, please deposit funds to meet the minimum requirement and send your Trader ID again."
                            )
                        except:
                            pass  # User might have blocked the bot
                
                # Add a small delay between checks to avoid overwhelming the bot
                await asyncio.sleep(1)
                
            except Exception as e:
                LOGGER.error(f"Error checking user {user_id}:{trader_id} in daily balance check: {e}")
                continue
        
        # Send summary to logs channel
        await app.send_message(
            LOGS_CHANNEL_ID,
            f"📊 **Daily Balance Check Summary**\n\n"
            f"**Total Users Checked:** {len(users)}\n"
            f"**Users Kicked:** {kicked_count}\n"
            f"**Check Completed:** {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}"
        )
        
        LOGGER.info(f"✅ Daily balance check completed - {kicked_count} users kicked out of {len(users)} checked")
        
    except Exception as e:
        LOGGER.error(f"❌ Error in daily balance check: {e}")
        await app.send_message(LOGS_CHANNEL_ID, f"❌ **Daily Balance Check Error**\n\n{str(e)}")


async def scheduled_turnover_check():
    """Function to be called by APScheduler every 15 days"""
    try:
        LOGGER.info("🔄 APScheduler: Starting automated turnover check")
        await check_turnover(None, None)
        LOGGER.info("✅ APScheduler: Turnover check completed")
        
        # Automatically schedule the next check in 15 days
        await schedule_next_turnover_check()
        
    except Exception as e:
        LOGGER.error(f"❌ APScheduler: Error in scheduled turnover check: {e}")


async def scheduled_daily_balance_check():
    """Function to be called by APScheduler daily"""
    try:
        LOGGER.info("🔄 APScheduler: Starting automated daily balance check")
        await daily_balance_check()
        LOGGER.info("✅ APScheduler: Daily balance check completed")
        
    except Exception as e:
        LOGGER.error(f"❌ APScheduler: Error in scheduled daily balance check: {e}")


async def setup_turnover_scheduler():
    """Setup APScheduler jobs for turnover checking and daily balance checking"""
    try:
        # Remove existing jobs if they exist (prevents duplicates on restart)
        try:
            scheduler.remove_job('turnover_check')
            LOGGER.info("Removed existing turnover check job")
        except:
            pass
        
        try:
            scheduler.remove_job('initial_turnover_check')
        except:
            pass
            
        try:
            scheduler.remove_job('daily_balance_check')
            LOGGER.info("Removed existing daily balance check job")
        except:
            pass
        
        # Get the last check time from database
        last_check = await get_last_turnover_check()
        current_time = datetime.now(timezone.utc)
        
        if last_check is None:
            # No previous check, schedule first check in 1 minute
            next_run_time = current_time + timedelta(minutes=1)
            LOGGER.info("🚀 No previous check found - scheduling initial check in 1 minute")
        else:
            # Calculate days since last check
            # Ensure last_check is timezone-aware (assume UTC if naive)
            if last_check.tzinfo is None:
                last_check = last_check.replace(tzinfo=timezone.utc)
            days_since_last_check = (current_time - last_check).days
            
            if days_since_last_check >= 15:
                # Overdue - schedule immediately
                next_run_time = current_time + timedelta(minutes=1)
                LOGGER.info(f"⚠️ Check is overdue by {days_since_last_check - 15} days - scheduling immediately")
            else:
                # Schedule for the remaining days to reach 15 days
                days_remaining = 15 - days_since_last_check
                next_run_time = current_time + timedelta(days=days_remaining)
                LOGGER.info(f"📅 Next check scheduled in {days_remaining} days (last check was {days_since_last_check} days ago)")
        
        # Add the specific date job
        scheduler.add_job(
            scheduled_turnover_check,
            'date',
            run_date=next_run_time,
            id='turnover_check',
            replace_existing=True,
            misfire_grace_time=3600  # 1 hour grace time if missed
        )
        
        LOGGER.info(f"✅ Turnover check scheduled for: {next_run_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        
        # Setup daily balance check (runs every day at 12:00 UTC)
        scheduler.add_job(
            scheduled_daily_balance_check,
            'cron',
            hour=12,
            minute=0,
            id='daily_balance_check',
            replace_existing=True,
            misfire_grace_time=3600  # 1 hour grace time if missed
        )
        
        LOGGER.info("✅ Daily balance check scheduled for 12:00 UTC every day")
            
    except Exception as e:
        LOGGER.error(f"❌ Error setting up schedulers: {e}")


async def schedule_next_turnover_check():
    """Reschedule the next turnover check 15 days from now"""
    try:
        # Remove the current job
        try:
            scheduler.remove_job('turnover_check')
        except:
            pass
        
        # Schedule next check in exactly 15 days
        next_run_time = datetime.now(timezone.utc) + timedelta(days=15)
        
        scheduler.add_job(
            scheduled_turnover_check,
            'date',
            run_date=next_run_time,
            id='turnover_check',
            replace_existing=True,
            misfire_grace_time=3600
        )
        
        # Schedule the next reschedule job
        scheduler.add_job(
            schedule_next_turnover_check,
            'date',
            run_date=next_run_time + timedelta(minutes=5),
            id='reschedule_turnover',
            replace_existing=True
        )
        
        LOGGER.info(f"🔄 Next turnover check rescheduled for: {next_run_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        
    except Exception as e:
        LOGGER.error(f"❌ Error rescheduling turnover check: {e}")


# Setup the scheduler when module loads (make it async)
asyncio.create_task(setup_turnover_scheduler())


@Client.on_message(filters.command("scheduler_status") & filters.user(ADMINS))
async def scheduler_status(client: Client, message: Message):
    """Admin command to check scheduler status"""
    try:
        jobs = scheduler.get_jobs()
        
        if not jobs:
            await message.reply_text("❌ **No scheduled jobs found**")
            return
        
        status_text = "📅 **Scheduler Status:**\n\n"
        
        for job in jobs:
            next_run = job.next_run_time
            if next_run:
                next_run_str = next_run.strftime('%Y-%m-%d %H:%M:%S UTC')
                status_text += f"🔄 **{job.id}**\n"
                status_text += f"   • Next run: {next_run_str}\n"
                status_text += f"   • Trigger: {job.trigger}\n\n"
            else:
                status_text += f"⏸️ **{job.id}** (No next run scheduled)\n\n"
        
        await message.reply_text(status_text)
        
    except Exception as e:
        LOGGER.error(f"Error checking scheduler status: {e}")
        await message.reply_text(f"❌ Error: {str(e)}")


@Client.on_message(filters.command("setup_scheduler") & filters.user(ADMINS))
async def manual_setup_scheduler(client: Client, message: Message):
    """Admin command to manually setup scheduler"""
    try:
        await message.reply_text("🔄 Setting up turnover scheduler...")
        await setup_turnover_scheduler()
        await message.reply_text("✅ Scheduler setup completed!")
    except Exception as e:
        LOGGER.error(f"Error setting up scheduler: {e}")
        await message.reply_text(f"❌ Error: {str(e)}")


@Client.on_message(filters.command("run_check_now") & filters.user(ADMINS))
async def run_check_now(client: Client, message: Message):
    """Admin command to run turnover check immediately"""
    try:
        await message.reply_text("🔄 Running turnover check now...")
        await scheduled_turnover_check()
        await message.reply_text("✅ Turnover check completed!")
    except Exception as e:
        LOGGER.error(f"Error running immediate check: {e}")
        await message.reply_text(f"❌ Error: {str(e)}")


@Client.on_message(filters.command("run_balance_check") & filters.user(ADMINS))
async def run_balance_check_now(client: Client, message: Message):
    """Admin command to run daily balance check immediately"""
    try:
        await message.reply_text("🔄 Running daily balance check now...")
        await daily_balance_check()
        await message.reply_text("✅ Daily balance check completed!")
    except Exception as e:
        LOGGER.error(f"Error running immediate balance check: {e}")
        await message.reply_text(f"❌ Error: {str(e)}")