import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from idverifier import DB_URL, LOGGER

# MongoDB setup
client = AsyncIOMotorClient(DB_URL)
db = client["tradeidverifier"]
forcesub_collection = db["forcesub"]

INSERTION_LOCK = asyncio.Lock()


async def set_channel(channel_id, channel_link):
    async with INSERTION_LOCK:
        try:
            existing = await forcesub_collection.find_one({})
            if existing:
                await forcesub_collection.update_one(
                    {"_id": existing["_id"]},
                    {"$set": {"channel_id": channel_id, "channel_link": channel_link}},
                )
            else:
                await forcesub_collection.insert_one(
                    {"channel_id": channel_id, "channel_link": channel_link}
                )
            return True
        except Exception as e:
            LOGGER.warning("Error setting force sub channel: %s", str(e))
            return False


async def get_channel():
    async with INSERTION_LOCK:
        try:
            channel = await forcesub_collection.find_one({})
            if channel:
                return channel["channel_id"]
            return False
        except Exception as e:
            LOGGER.warning("Error getting force sub channel: %s", str(e))
            return False


async def get_link():
    async with INSERTION_LOCK:
        try:
            channel = await forcesub_collection.find_one({})
            if channel:
                return channel["channel_link"]
            return False
        except Exception as e:
            LOGGER.warning("Error getting force sub link: %s", str(e))
            return False


async def delete_channel():
    async with INSERTION_LOCK:
        try:
            await forcesub_collection.delete_one({})
            return True
        except Exception as e:
            LOGGER.warning("Error deleting force sub channel: %s", str(e))
            return False
