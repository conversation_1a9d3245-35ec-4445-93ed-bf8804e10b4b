import asyncio
import time
import datetime
from pyrogram.types import Message
from pyrogram import Client, filters
from pyrogram.errors import Flood<PERSON><PERSON>
from pyrogram.enums import ChatAction
from idverifier import ADMI<PERSON>, OWNER_ID, LOGGER
from idverifier.db import get_user, del_user


@Client.on_message(filters.private & filters.command("stats") & filters.user(ADMINS))
async def get_subscribers_count(bot: Client, message: Message):
    wait_msg = "__Calculating, please wait...__"
    msg = await message.reply_text(wait_msg, quote=True)
    active, blocked = await users_info(bot)
    stats_msg = f"**Stats**\nSubscribers: `{active}`\nBlocked / Deleted: `{blocked}`"
    await msg.edit(stats_msg)


@Client.on_message(
    filters.private & filters.command("broadcast") & filters.user(OWNER_ID)
)
async def send_text(bot, message: Message):
    user_id = message.from_user.id
    if message.reply_to_message is None:
        msg = await message.reply_text(
            "`Please reply to a message to broadcast it.`", quote=True
        )
        await asyncio.sleep(8)
        await msg.delete()
        return

    start_time = time.time()
    await message.reply_text("Starting broadcast, content below...", quote=True)
    await bot.copy_message(
        chat_id=user_id,
        from_chat_id=message.chat.id,
        message_id=message.reply_to_message_id,
        reply_markup=message.reply_to_message.reply_markup,
    )
    users = await get_user()
    success = 0
    failed = 0
    for chat_id in users:
        br_msg = bool()
        try:
            br_msg = await bot.copy_message(
                chat_id=chat_id,
                from_chat_id=message.chat.id,
                message_id=message.reply_to_message_id,
                reply_markup=message.reply_to_message.reply_markup,
            )
            LOGGER.info("Broadcast sent to %s", chat_id)
        except FloodWait as e:
            LOGGER.warning("Floodwait while broadcasting, sleeping for %s", e.value)
            await asyncio.sleep(e.value)
        except Exception:
            pass

        if bool(br_msg):
            success += 1
        else:
            failed += 1
    time_taken = datetime.timedelta(seconds=int(time.time() - start_time))
    await message.reply_text(
        f"**Broadcast Completed**\nSent to: `{success}`\nBlocked / Deleted: `{failed}`\nCompleted in `{time_taken}` hh:mm:ss",
        quote=True,
    )


async def users_info(bot):
    user_count = 0
    blocked = 0
    users = await get_user()
    for user in users:
        chat_ac = bool()
        try:
            chat_ac = await bot.send_chat_action(user, ChatAction.TYPING)
        except FloodWait as e:
            await asyncio.sleep(e.value)
        except Exception:
            pass
        if bool(chat_ac):
            user_count += 1
        else:
            await del_user(user)
            LOGGER.info("Deleted user id %s from broadcast list", user)
            blocked += 1
    return user_count, blocked
