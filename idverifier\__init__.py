import os
import sys
import re
import logging
import logging.config
from dotenv import load_dotenv


load_dotenv()

pattern = re.compile(r"^.\d+$")

# vars
APP_ID = os.environ.get("APP_ID", "")
API_HASH = os.environ.get("API_HASH", "")
SESSION = os.environ.get("SESSION", "")
BOT_TOKEN = os.environ.get("BOT_TOKEN", "")
OWNER_ID = int(os.environ.get("OWNER_ID", ""))
ADMINS = [
    int(user) if pattern.search(user) else user
    for user in os.environ.get("ADMINS", "").split()
] + [OWNER_ID]
CHANNEL_ID = [
    int(chat) if pattern.search(chat) else chat
    for chat in os.environ.get("CHANNEL_ID", "").split()
]
LOGS_CHANNEL_ID = int(os.environ.get("LOGS_CHANNEL_ID", "0"))
VIP_GROUP_ID = int(os.environ.get("VIP_GROUP_ID", "0"))
MONGO_DB_URL = os.environ.get("MONGO_DB_URL", "")
if MONGO_DB_URL:
    DB_URL = MONGO_DB_URL
else:
    print("No database URL found")
    sys.exit(1)

# logging Conf
logging.config.fileConfig(fname="config.ini", disable_existing_loggers=False)
LOGGER = logging.getLogger(__name__)
logging.getLogger("pyrogram").setLevel(logging.WARNING)
logging.getLogger("apscheduler").setLevel(logging.WARNING)
