import uvloop
import logging

uvloop.install()

import asyncio # noqa
from pyrogram import Client, __version__, idle  # noqa: E402
from pyrogram.raw.all import layer  # noqa: E402
from idverifier import APP_ID, API_HASH, BOT_TOKEN, SESSION, LOGGER  # noqa: E402
# from idverifier.scheduler import start_scheduler, stop_scheduler # noqa: E402


app = None
user = None


async def main():
    global app, user
    plugins = dict(root="idverifier/plugins")
    
    app = Client(
        name="idverifier",
        api_id=APP_ID,
        api_hash=API_HASH,
        bot_token=BOT_TOKEN,
        plugins=plugins,
        # skip_updates=False,
    )
    
    user = Client(
        name="user_idverifier",
        api_id=APP_ID,
        api_hash=API_HASH,
        session_string=SESSION,
    )
    
    async with app, user:
        bot_me = app.me
        user_me = user.me

        
        LOGGER.info(
            "Bot: %s - @%s - Pyrogram v%s (Layer %s) - Started...",
            bot_me.first_name,
            bot_me.username,
            __version__,
            layer,
        )
        
        LOGGER.info(
            "User: %s - @%s - Started...",
            user_me.first_name,
            user_me.username,
        )
        
        # Start the scheduler for turnover checks
        # start_scheduler()
        LOGGER.info("Bot started successfully with scheduler!")
        
        await idle()
        
        LOGGER.info("Bot: %s - @%s - Stopped !!!", bot_me.first_name, bot_me.username)
        LOGGER.info("User: %s - @%s - Stopped !!!", user_me.first_name, user_me.username)

        # Stop the scheduler when bot is shutting down
        # stop_scheduler()


if __name__ == "__main__":
    try:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
    except KeyboardInterrupt:
        LOGGER.info("Bot stopped by user")
    finally:
        # Stop the scheduler when bot is shutting down
        # stop_scheduler()
        print("Bot stopped by user")
