import os
import sys
import asyncio
from pyrogram import Client, filters
from pyrogram.types import Message
from idverifier.db import add_user
# from pyrogram.types import LinkPreviewOptions
from idverifier import LOGGER, ADMINS
from idverifier.utils.constants import START_MSG


@Client.on_message(filters.command("start") & filters.private)
async def start_command(client: Client, message: Message):
    user_id = message.from_user.id
    name = message.from_user.first_name if message.from_user.first_name else " "
    mention = message.from_user.mention
    user_name = "@" + message.from_user.username if message.from_user.username else None
    await add_user(user_id, user_name)
    await message.reply_text(
        START_MSG.format(mention),
    )


@Client.on_message(filters.command("help") & filters.user(ADMINS))
async def help_command(client: Client, message: Message):
    help_text = (
        "**Admin Commands:**\n\n"
        "`/addchat CHAT_ID` - Add a chat to the system.\n"
        "`/removechat CHAT_ID` - Remove a chat from the system.\n"
        "`/chats` - List all active chats.\n"
        "`/help` - Show this help message.\n\n"
        "`/broadcast` - Broadcast a message to all users.\n"
    )
    await message.reply_text(help_text)



@Client.on_message(filters.command(["restart"]) & filters.user(ADMINS))
async def restart(bot, update):
    LOGGER.warning("Restarting bot using /restart command")
    msg = await update.reply_text(text="__Restarting.....__", quote=True)
    await asyncio.sleep(5)
    await msg.edit("__Bot restarted !__")
    os.execv(sys.executable, ["python3", "-m", "traderbot"] + sys.argv)


@Client.on_message(filters.command(["logs"]) & filters.user(ADMINS))
async def log_file(bot, update):
    logs_msg = await update.reply("__Sending logs, please wait...__", quote=True)
    try:
        await update.reply_document("logs.txt")
    except Exception as e:
        await update.reply(str(e))
    await logs_msg.delete()


