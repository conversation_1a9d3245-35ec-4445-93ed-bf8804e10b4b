from pyrogram import Client, filters
from idverifier import ADMINS
from idverifier.db import is_banned, ban_user, unban_user


@Client.on_message(filters.command(["ban"]) & filters.user(ADMINS))
async def banuser(bot, update):
    data = update.text.split()
    if len(data) == 2:
        user_id = data[-1]
        banned = await is_banned(int(user_id))
        if not banned:
            await ban_user(int(user_id))
            await update.reply_text(f"User {user_id} banned", quote=True)
        else:
            await update.reply_text(f"User {user_id} is already banned", quote=True)

    else:
        await update.reply_text(
            "Please send in proper format `/ban user_id`", quote=True
        )


@Client.on_message(filters.command(["unban"]) & filters.user(ADMINS))
async def unbanuser(bot, update):
    data = update.text.split()
    if len(data) == 2:
        user_id = data[-1]
        banned = await is_banned(int(user_id))
        if banned:
            await unban_user(int(user_id))
            await update.reply_text(f"User {user_id} unbanned", quote=True)
        else:
            await update.reply_text(f"User {user_id} is not in ban list", quote=True)
    else:
        await update.reply_text(
            "Please send in proper format `/unban user_id`", quote=True
        )
