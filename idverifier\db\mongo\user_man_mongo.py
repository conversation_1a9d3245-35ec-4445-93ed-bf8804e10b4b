import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from idverifier import DB_URL, LOGGER
from datetime import datetime, timezone


client = AsyncIOMotorClient(DB_URL)
db = client["tradeidverifier"]
verification_collection = db["verifications"]
turnover_collection = db["turnover_history"]

INSERTION_LOCK = asyncio.Lock()


async def add_verified_user(user_id: int, trader_id: str, balance: float, username: str = None, first_name: str = None):
    """Add a verified user to the database"""
    async with INSERTION_LOCK:
        try:
            user_data = {
                "user_id": user_id,
                "trader_id": trader_id,
                "balance": balance,
                "username": username,
                "first_name": first_name,
                "verified_at": datetime.now(timezone.utc),
                "is_active": True
            }
            
            # Check if user already exists
            existing_user = await verification_collection.find_one({"user_id": user_id})
            
            if existing_user:
                # Update existing user
                await verification_collection.update_one(
                    {"user_id": user_id},
                    {"$set": user_data}
                )
                LOGGER.info(f"Updated verified user: {user_id} with trader ID: {trader_id}")
            else:
                # Insert new user
                await verification_collection.insert_one(user_data)
                LOGGER.info(f"Added new verified user: {user_id} with trader ID: {trader_id}")
            
            return True
        except Exception as e:
            LOGGER.error(f"Error adding verified user {user_id}: {e}")
            return False


async def get_verified_user(user_id: int):
    """Get verified user data by user ID"""
    async with INSERTION_LOCK:
        try:
            user = await verification_collection.find_one({"user_id": user_id})
            return user
        except Exception as e:
            LOGGER.error(f"Error getting verified user {user_id}: {e}")
            return None


async def get_user_by_trader_id(trader_id: str):
    """Get verified user data by trader ID"""
    async with INSERTION_LOCK:
        try:
            user = await verification_collection.find_one({"trader_id": trader_id})
            return user
        except Exception as e:
            LOGGER.error(f"Error getting user by trader ID {trader_id}: {e}")
            return None


async def is_user_verified(user_id: int):
    """Check if user is verified"""
    async with INSERTION_LOCK:
        try:
            user = await verification_collection.find_one({"user_id": user_id, "is_active": True})
            return user is not None
        except Exception as e:
            LOGGER.error(f"Error checking verification status for user {user_id}: {e}")
            return False



async def remove_user(user_id: int):
    """Completely remove a user from the database"""
    async with INSERTION_LOCK:
        try:
            # Get user info before deletion for logging
            user_data = await verification_collection.find_one({"user_id": user_id})
            
            if user_data:
                trader_id = user_data.get('trader_id', 'Unknown')
                username = user_data.get('username', 'Unknown')
                
                # Delete the user record
                result = await verification_collection.delete_one({"user_id": user_id})
                
                if result.deleted_count > 0:
                    LOGGER.info(f"Completely removed user {user_id} (trader: {trader_id}, username: {username}) from database")
                    return True
                else:
                    LOGGER.warning(f"No user found with ID {user_id} to remove")
                    return False
            else:
                LOGGER.warning(f"User {user_id} not found in database for removal")
                return False
                
        except Exception as e:
            LOGGER.error(f"Error removing user {user_id}: {e}")
            return False


async def get_all_verified_users():
    """Get all verified users"""
    async with INSERTION_LOCK:
        try:
            users = []
            async for user in verification_collection.find({"is_active": True}):
                users.append(user)
            return users
        except Exception as e:
            LOGGER.error(f"Error getting all verified users: {e}")
            return []


async def update_user_balance(user_id: int, new_balance: float):
    """Update user's balance"""
    async with INSERTION_LOCK:
        try:
            result = await verification_collection.update_one(
                {"user_id": user_id},
                {"$set": {"balance": new_balance, "updated_at": datetime.now(timezone.utc)}}
            )
            if result.modified_count > 0:
                LOGGER.info(f"Updated balance for user {user_id}: ${new_balance}")
                return True
            return False
        except Exception as e:
            LOGGER.error(f"Error updating balance for user {user_id}: {e}")
            return False


async def save_turnover_record(user_id: int, trader_id: str, turnover: float, days_since_verification: int):
    """Save turnover record for a user"""
    async with INSERTION_LOCK:
        try:
            turnover_data = {
                "user_id": user_id,
                "trader_id": trader_id,
                "turnover": turnover,
                "days_since_verification": days_since_verification,
                "recorded_at": datetime.now(timezone.utc)
            }
            
            await turnover_collection.insert_one(turnover_data)
            LOGGER.info(f"Saved turnover record for user {user_id}: ${turnover} at day {days_since_verification}")
            return True
        except Exception as e:
            LOGGER.error(f"Error saving turnover record for user {user_id}: {e}")
            return False


async def get_turnover_history(user_id: int):
    """Get turnover history for a user"""
    async with INSERTION_LOCK:
        try:
            records = []
            async for record in turnover_collection.find({"user_id": user_id}).sort("recorded_at", -1):
                records.append(record)
            return records
        except Exception as e:
            LOGGER.error(f"Error getting turnover history for user {user_id}: {e}")
            return []


async def get_last_turnover_check():
    """Get the timestamp of the last turnover check"""
    async with INSERTION_LOCK:
        try:
            # Find the most recent turnover record
            record = await turnover_collection.find_one({}, sort=[("recorded_at", -1)])
            return record["recorded_at"] if record else None
        except Exception as e:
            LOGGER.error(f"Error getting last turnover check: {e}")
            return None




