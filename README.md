# Telegram Auto Forward Bot



## Deployment

Run Locally / On VPS

```bash
# Clone this repo
git clone https://github.com/

# cd folder
cd traderbot

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
venv\Scripts\activate # For Windows
source venv/bin/activate # For Linux or MacOS

# Install Packages
pip3 install -r requirements.txt

# Copy .env.sample file & add variables
cp .env.sample .env

# Run bot
python3 -m traderbot
```
